# Tetromino - Kotlin/Multiplatform 俄罗斯方块

一个使用 **TDD（测试驱动开发）** 方法构建的 Kotlin/Multiplatform 俄罗斯方块游戏，专为 **WASM (WebAssembly)** 目标平台设计。

## 🎮 游戏特性

- ✅ **7种标准俄罗斯方块形状**：I、O、T、S、Z、J、L
- ✅ **完整的游戏机制**：移动、旋转、软下落、硬下落
- ✅ **智能碰撞检测**：边界检测和方块碰撞
- ✅ **满行清除系统**：自动检测并清除满行
- ✅ **分数计算系统**：基于清除行数的分数奖励
- ✅ **等级系统**：每清除10行升一级
- ✅ **游戏状态管理**：开始、暂停、恢复、游戏结束
- ✅ **渲染系统**：支持 Canvas 渲染和控制台输出

## 🏗️ 技术架构

### 核心技术栈
- **Kotlin/Multiplatform 1.9.22**
- **WASM (WebAssembly)** 目标平台
- **TDD 测试驱动开发**
- **函数式编程范式**（不可变数据结构）

### 项目结构
```
src/
├── commonMain/kotlin/com/tetromino/     # 核心游戏逻辑
│   ├── Position.kt                      # 坐标系统
│   ├── GameBoard.kt                     # 游戏板管理
│   ├── TetrominoType.kt                 # 方块类型定义
│   ├── Tetromino.kt                     # 方块实体
│   ├── GameState.kt                     # 游戏状态管理
│   ├── InputHandler.kt                  # 输入处理
│   ├── Renderer.kt                      # 渲染系统
│   └── TetrisGame.kt                    # 游戏主控制器
├── commonTest/kotlin/com/tetromino/     # 单元测试 (50+ 测试)
└── wasmJsMain/kotlin/com/tetromino/     # WASM 入口点
    └── Main.kt
```

## 🚀 快速开始

### 环境要求
- **JDK 11+**
- **Gradle 8.0+**
- **现代浏览器**（支持 WebAssembly）

### 1. 克隆项目
```bash
git clone <repository-url>
cd augment-tetromino
```

### 2. 运行测试
```bash
# 运行所有测试
./gradlew wasmJsTest

# 查看测试报告
open build/reports/tests/wasmJsTest/index.html
```

### 3. 构建 WASM 应用
```bash
# 构建开发版本
./gradlew wasmJsBrowserDevelopmentWebpack

# 构建生产版本
./gradlew wasmJsBrowserProductionWebpack
```

### 4. 运行开发服务器
```bash
# 启动开发服务器
./gradlew wasmJsBrowserDevelopmentRun

# 浏览器访问 http://localhost:8080
```

## 🎯 游戏控制

| 按键 | 功能 |
|------|------|
| ← → | 左右移动 |
| ↓ | 软下落（加速下降） |
| ↑ | 顺时针旋转 |
| Space | 硬下落（瞬间到底） |
| P | 暂停/恢复游戏 |

## 🧪 测试驱动开发 (TDD)

本项目严格遵循 TDD 开发流程：

### TDD 循环
1. **🔴 RED**：编写失败的测试
2. **🟢 GREEN**：编写最小代码让测试通过
3. **🔵 REFACTOR**：重构代码提高质量

### 测试覆盖
- **50+ 单元测试**
- **100% 核心业务逻辑覆盖**
- **边界条件和异常情况测试**

### 运行特定测试
```bash
# 运行特定测试类
./gradlew wasmJsTest --tests "*PositionTest*"

# 运行特定测试方法
./gradlew wasmJsTest --tests "*shouldCreatePositionWithCorrectCoordinates*"
```

## 📊 分数系统

| 清除行数 | 得分 |
|----------|------|
| 1 行 | 100 分 |
| 2 行 | 300 分 |
| 3 行 | 500 分 |
| 4 行 (Tetris) | 800 分 |

- **等级提升**：每清除 10 行升一级
- **游戏速度**：等级越高，方块下落越快

## 🏛️ 架构设计

### 核心设计原则
- **单一职责原则**：每个类只负责一个功能
- **不可变性**：所有数据结构都是不可变的
- **纯函数**：无副作用的函数设计
- **依赖注入**：松耦合的组件设计

### 主要组件

#### 1. 游戏核心 (`TetrisGame`)
- 游戏主控制器
- 协调各个组件
- 管理游戏循环

#### 2. 游戏板 (`GameBoard`)
- 管理游戏区域状态
- 碰撞检测
- 满行清除

#### 3. 方块系统 (`Tetromino` + `TetrominoType`)
- 7种标准方块形状
- 旋转变换算法
- 位置计算

#### 4. 输入处理 (`InputHandler`)
- 键盘输入映射
- 动作验证
- 碰撞检查

#### 5. 渲染系统 (`Renderer`)
- 渲染数据生成
- 视图状态管理
- Canvas 绘制支持

## 🔧 开发工具

### Gradle 任务
```bash
# 构建相关
./gradlew build                          # 完整构建
./gradlew clean                          # 清理构建

# 测试相关
./gradlew test                           # 运行所有测试
./gradlew wasmJsTest                     # 运行 WASM 测试
./gradlew testReport                     # 生成测试报告

# WASM 相关
./gradlew wasmJsBrowserDevelopmentRun    # 开发服务器
./gradlew wasmJsBrowserProductionRun     # 生产服务器
./gradlew wasmJsBrowserDevelopmentWebpack # 开发构建
./gradlew wasmJsBrowserProductionWebpack  # 生产构建
```

### IDE 支持
- **IntelliJ IDEA**：完整支持 Kotlin/Multiplatform
- **VS Code**：通过 Kotlin 插件支持
- **Android Studio**：原生支持

## 🐛 故障排除

### 常见问题

#### 1. WASM 构建失败
```bash
# 清理并重新构建
./gradlew clean
./gradlew wasmJsBrowserDevelopmentWebpack
```

#### 2. 测试失败
```bash
# 查看详细测试输出
./gradlew wasmJsTest --info
```

#### 3. 浏览器兼容性
确保使用支持 WebAssembly 的现代浏览器：
- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## 📈 性能优化

### WASM 优化
- 使用生产构建：`./gradlew wasmJsBrowserProductionWebpack`
- 启用代码压缩和优化
- 减少不必要的依赖

### 游戏性能
- 高效的碰撞检测算法
- 最小化对象创建
- 优化渲染循环

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. **编写测试**（TDD 第一步）
4. 实现功能
5. 确保所有测试通过：`./gradlew wasmJsTest`
6. 提交代码：`git commit -m "feat: add new feature"`
7. 推送分支：`git push origin feature/new-feature`
8. 创建 Pull Request

### 代码规范
- 遵循 Kotlin 官方编码规范
- 使用有意义的变量和函数名
- 编写清晰的注释
- 保持测试覆盖率

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- **Kotlin 团队**：提供优秀的多平台开发工具
- **TDD 社区**：推广测试驱动开发最佳实践
- **俄罗斯方块**：经典游戏设计灵感

---

**Happy Coding! 🎮✨**
