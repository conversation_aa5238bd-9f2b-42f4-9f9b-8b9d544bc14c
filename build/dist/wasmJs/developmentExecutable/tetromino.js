/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["augment-tetromino"] = factory();
	else
		root["augment-tetromino"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./kotlin/tetromino.mjs":
/*!******************************!*\
  !*** ./kotlin/tetromino.mjs ***!
  \******************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _tetromino_uninstantiated_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tetromino.uninstantiated.mjs */ \"./kotlin/tetromino.uninstantiated.mjs\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((await (0,_tetromino_uninstantiated_mjs__WEBPACK_IMPORTED_MODULE_0__.instantiate)()).exports);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } }, 1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9rb3RsaW4vdGV0cm9taW5vLm1qcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQzZEO0FBQzdELGlFQUFlLE9BQU8sMEVBQVcsV0FBVyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXVnbWVudC10ZXRyb21pbm8vLi9rb3RsaW4vdGV0cm9taW5vLm1qcz8yNDMxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuaW1wb3J0IHsgaW5zdGFudGlhdGUgfSBmcm9tICcuL3RldHJvbWluby51bmluc3RhbnRpYXRlZC5tanMnO1xuZXhwb3J0IGRlZmF1bHQgKGF3YWl0IGluc3RhbnRpYXRlKCkpLmV4cG9ydHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./kotlin/tetromino.mjs\n");

/***/ }),

/***/ "./kotlin/tetromino.uninstantiated.mjs":
/*!*********************************************!*\
  !*** ./kotlin/tetromino.uninstantiated.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"instantiate\": () => (/* binding */ instantiate)\n/* harmony export */ });\n\nasync function instantiate(imports={}, runInitializer=true) {\n    const externrefBoxes = new WeakMap();\n    // ref must be non-null\n    function tryGetOrSetExternrefBox(ref, ifNotCached) {\n        if (typeof ref !== 'object') return ifNotCached;\n        const cachedBox = externrefBoxes.get(ref);\n        if (cachedBox !== void 0) return cachedBox;\n        externrefBoxes.set(ref, ifNotCached);\n        return ifNotCached;\n    }\n\n\n    \n    const js_code = {\n        'kotlin.captureStackTrace' : () => new Error().stack,\n        'kotlin.wasm.internal.throwJsError' : (message, wasmTypeName, stack) => { \n            const error = new Error();\n            error.message = message;\n            error.name = wasmTypeName;\n            error.stack = stack;\n            throw error;\n             },\n        'kotlin.wasm.internal.stringLength' : (x) => x.length,\n        'kotlin.wasm.internal.jsExportStringToWasm' : (src, srcOffset, srcLength, dstAddr) => { \n            const mem16 = new Uint16Array(wasmExports.memory.buffer, dstAddr, srcLength);\n            let arrayIndex = 0;\n            let srcIndex = srcOffset;\n            while (arrayIndex < srcLength) {\n                mem16.set([src.charCodeAt(srcIndex)], arrayIndex);\n                srcIndex++;\n                arrayIndex++;\n            }     \n             },\n        'kotlin.wasm.internal.importStringFromWasm' : (address, length, prefix) => { \n            const mem16 = new Uint16Array(wasmExports.memory.buffer, address, length);\n            const str = String.fromCharCode.apply(null, mem16);\n            return (prefix == null) ? str : prefix + str;\n             },\n        'kotlin.wasm.internal.getJsEmptyString' : () => '',\n        'kotlin.wasm.internal.externrefToString' : (ref) => String(ref),\n        'kotlin.wasm.internal.externrefEquals' : (lhs, rhs) => lhs === rhs,\n        'kotlin.wasm.internal.externrefHashCode' : \n        (() => {\n        const dataView = new DataView(new ArrayBuffer(8));\n        function numberHashCode(obj) {\n            if ((obj | 0) === obj) {\n                return obj | 0;\n            } else {\n                dataView.setFloat64(0, obj, true);\n                return (dataView.getInt32(0, true) * 31 | 0) + dataView.getInt32(4, true) | 0;\n            }\n        }\n        \n        const hashCodes = new WeakMap();\n        function getObjectHashCode(obj) {\n            const res = hashCodes.get(obj);\n            if (res === undefined) {\n                const POW_2_32 = 4294967296;\n                const hash = (Math.random() * POW_2_32) | 0;\n                hashCodes.set(obj, hash);\n                return hash;\n            }\n            return res;\n        }\n        \n        function getStringHashCode(str) {\n            var hash = 0;\n            for (var i = 0; i < str.length; i++) {\n                var code  = str.charCodeAt(i);\n                hash  = (hash * 31 + code) | 0;\n            }\n            return hash;\n        }\n        \n        return (obj) => {\n            if (obj == null) {\n                return 0;\n            }\n            switch (typeof obj) {\n                case \"object\":\n                case \"function\":\n                    return getObjectHashCode(obj);\n                case \"number\":\n                    return numberHashCode(obj);\n                case \"boolean\":\n                    return obj ? 1231 : 1237;\n                default:\n                    return getStringHashCode(String(obj)); \n            }\n        }\n        })(),\n        'kotlin.wasm.internal.isNullish' : (ref) => ref == null,\n        'kotlin.wasm.internal.externrefToInt' : (ref) => Number(ref),\n        'kotlin.wasm.internal.externrefToBoolean' : (ref) => Boolean(ref),\n        'kotlin.wasm.internal.externrefToLong' : (ref) => Number(ref),\n        'kotlin.wasm.internal.externrefToFloat' : (ref) => Number(ref),\n        'kotlin.wasm.internal.externrefToDouble' : (ref) => Number(ref),\n        'kotlin.wasm.internal.intToExternref' : (x) => x,\n        'kotlin.wasm.internal.getJsTrue' : () => true,\n        'kotlin.wasm.internal.getJsFalse' : () => false,\n        'kotlin.wasm.internal.longToExternref' : (x) => x,\n        'kotlin.wasm.internal.floatToExternref' : (x) => x,\n        'kotlin.wasm.internal.doubleToExternref' : (x) => x,\n        'kotlin.wasm.internal.newJsArray' : () => [],\n        'kotlin.wasm.internal.jsArrayPush' : (array, element) => { array.push(element); },\n        'kotlin.wasm.internal.tryGetOrSetExternrefBox_$external_fun' : (p0, p1) => tryGetOrSetExternrefBox(p0, p1),\n        'kotlin.io.printlnImpl' : (message) => console.log(message),\n        'kotlin.io.printImpl' : (message) => typeof write !== 'undefined' ? write(message) : console.log(message),\n        'kotlin.js.JsBoolean_$external_fun' : () => new JsBoolean(),\n        'kotlin.js.JsBoolean_$external_class_instanceof' : (x) => typeof x === 'boolean',\n        'kotlin.js.JsNumber_$external_fun' : () => new JsNumber(),\n        'kotlin.js.JsNumber_$external_class_instanceof' : (x) => typeof x === 'number',\n        'kotlin.js.JsString_$external_fun' : () => new JsString(),\n        'kotlin.js.JsString_$external_class_instanceof' : (x) => typeof x === 'string',\n        'kotlin.random.initialSeed' : () => ((Math.random() * Math.pow(2, 32)) | 0)\n    }\n    \n    // Placed here to give access to it from externals (js_code)\n    let wasmInstance;\n    let require; \n    let wasmExports;\n\n    const isNodeJs = (typeof process !== 'undefined') && (process.release.name === 'node');\n    const isStandaloneJsVM =\n        !isNodeJs && (\n            typeof d8 !== 'undefined' // V8\n            || typeof inIon !== 'undefined' // SpiderMonkey\n            || typeof jscOptions !== 'undefined' // JavaScriptCore\n        );\n    const isBrowser = !isNodeJs && !isStandaloneJsVM && (typeof window !== 'undefined');\n    \n    if (!isNodeJs && !isStandaloneJsVM && !isBrowser) {\n      throw \"Supported JS engine not detected\";\n    }\n    \n    const wasmFilePath = './tetromino.wasm';\n    const importObject = {\n        js_code,\n\n    };\n    \n    try {\n      if (isNodeJs) {\n        const module = await import(/* webpackIgnore: true */'node:module');\n        require = module.default.createRequire(\"file:///Users/<USER>/code/MyWorks/aitools/augment-tetromino/build/js/packages/tetromino/kotlin/tetromino.uninstantiated.mjs\");\n        const fs = require('fs');\n        const path = require('path');\n        const url = require('url');\n        const filepath = url.fileURLToPath(\"file:///Users/<USER>/code/MyWorks/aitools/augment-tetromino/build/js/packages/tetromino/kotlin/tetromino.uninstantiated.mjs\");\n        const dirpath = path.dirname(filepath);\n        const wasmBuffer = fs.readFileSync(path.resolve(dirpath, wasmFilePath));\n        const wasmModule = new WebAssembly.Module(wasmBuffer);\n        wasmInstance = new WebAssembly.Instance(wasmModule, importObject);\n      }\n      \n      if (isStandaloneJsVM) {\n        const wasmBuffer = read(wasmFilePath, 'binary');\n        const wasmModule = new WebAssembly.Module(wasmBuffer);\n        wasmInstance = new WebAssembly.Instance(wasmModule, importObject);\n      }\n      \n      if (isBrowser) {\n        wasmInstance = (await WebAssembly.instantiateStreaming(fetch(wasmFilePath), importObject)).instance;\n      }\n    } catch (e) {\n      if (e instanceof WebAssembly.CompileError) {\n        let text = `Please make sure that your runtime environment supports the latest version of Wasm GC and Exception-Handling proposals.\nFor more information, see https://kotl.in/wasm-help\n`;\n        if (isBrowser) {\n          console.error(text);\n        } else {\n          const t = \"\\n\" + text;\n          if (typeof console !== \"undefined\" && console.log !== void 0) \n            console.log(t);\n          else \n            print(t);\n        }\n      }\n      throw e;\n    }\n    \n    wasmExports = wasmInstance.exports;\n    if (runInitializer) {\n        wasmExports._initialize();\n    }\n\n    return { instance: wasmInstance,  exports: wasmExports };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./kotlin/tetromino.uninstantiated.mjs\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/async module */
/******/ 	(() => {
/******/ 		var webpackQueues = typeof Symbol === "function" ? Symbol("webpack queues") : "__webpack_queues__";
/******/ 		var webpackExports = typeof Symbol === "function" ? Symbol("webpack exports") : "__webpack_exports__";
/******/ 		var webpackError = typeof Symbol === "function" ? Symbol("webpack error") : "__webpack_error__";
/******/ 		var resolveQueue = (queue) => {
/******/ 			if(queue && !queue.d) {
/******/ 				queue.d = 1;
/******/ 				queue.forEach((fn) => (fn.r--));
/******/ 				queue.forEach((fn) => (fn.r-- ? fn.r++ : fn()));
/******/ 			}
/******/ 		}
/******/ 		var wrapDeps = (deps) => (deps.map((dep) => {
/******/ 			if(dep !== null && typeof dep === "object") {
/******/ 				if(dep[webpackQueues]) return dep;
/******/ 				if(dep.then) {
/******/ 					var queue = [];
/******/ 					queue.d = 0;
/******/ 					dep.then((r) => {
/******/ 						obj[webpackExports] = r;
/******/ 						resolveQueue(queue);
/******/ 					}, (e) => {
/******/ 						obj[webpackError] = e;
/******/ 						resolveQueue(queue);
/******/ 					});
/******/ 					var obj = {};
/******/ 					obj[webpackQueues] = (fn) => (fn(queue));
/******/ 					return obj;
/******/ 				}
/******/ 			}
/******/ 			var ret = {};
/******/ 			ret[webpackQueues] = x => {};
/******/ 			ret[webpackExports] = dep;
/******/ 			return ret;
/******/ 		}));
/******/ 		__webpack_require__.a = (module, body, hasAwait) => {
/******/ 			var queue;
/******/ 			hasAwait && ((queue = []).d = 1);
/******/ 			var depQueues = new Set();
/******/ 			var exports = module.exports;
/******/ 			var currentDeps;
/******/ 			var outerResolve;
/******/ 			var reject;
/******/ 			var promise = new Promise((resolve, rej) => {
/******/ 				reject = rej;
/******/ 				outerResolve = resolve;
/******/ 			});
/******/ 			promise[webpackExports] = exports;
/******/ 			promise[webpackQueues] = (fn) => (queue && fn(queue), depQueues.forEach(fn), promise["catch"](x => {}));
/******/ 			module.exports = promise;
/******/ 			body((deps) => {
/******/ 				currentDeps = wrapDeps(deps);
/******/ 				var fn;
/******/ 				var getResult = () => (currentDeps.map((d) => {
/******/ 					if(d[webpackError]) throw d[webpackError];
/******/ 					return d[webpackExports];
/******/ 				}))
/******/ 				var promise = new Promise((resolve) => {
/******/ 					fn = () => (resolve(getResult));
/******/ 					fn.r = 0;
/******/ 					var fnQueue = (q) => (q !== queue && !depQueues.has(q) && (depQueues.add(q), q && !q.d && (fn.r++, q.push(fn))));
/******/ 					currentDeps.map((dep) => (dep[webpackQueues](fnQueue)));
/******/ 				});
/******/ 				return fn.r ? promise : getResult();
/******/ 			}, (err) => ((err ? reject(promise[webpackError] = err) : outerResolve(exports)), resolveQueue(queue)));
/******/ 			queue && (queue.d = 0);
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./kotlin/tetromino.mjs");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});