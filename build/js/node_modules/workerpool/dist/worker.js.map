{"version": 3, "file": "worker.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA;AACA,IAAIA,kBAAkB,GAAGC,IAAI,CACzB,qCACA,YADA,GAEA,gFAHyB,CAA7B;AAMA;AACA;AACA;AACA;;AACA,IAAIC,mBAAmB,GAAG,0BAA1B,EAEA;AAEA;AACA;;AACA,IAAIC,MAAM,GAAG;AACXC,EAAAA,IAAI,EAAE,gBAAW,CAAE;AADR,CAAb;;AAGA,IAAI,OAAOC,IAAP,KAAgB,WAAhB,IAA+B,OAAOC,WAAP,KAAuB,UAAtD,IAAoE,OAAOC,gBAAP,KAA4B,UAApG,EAAgH;AAC9G;AACAJ,EAAAA,MAAM,CAACK,EAAP,GAAY,UAAUC,KAAV,EAAiBC,QAAjB,EAA2B;AACrCH,IAAAA,gBAAgB,CAACE,KAAD,EAAQ,UAAUE,OAAV,EAAmB;AACzCD,MAAAA,QAAQ,CAACC,OAAO,CAACC,IAAT,CAAR;AACD,KAFe,CAAhB;AAGD,GAJD;;AAKAT,EAAAA,MAAM,CAACU,IAAP,GAAc,UAAUF,OAAV,EAAmB;AAC/BL,IAAAA,WAAW,CAACK,OAAD,CAAX;AACD,GAFD;AAGD,CAVD,MAWK,IAAI,OAAOG,OAAP,KAAmB,WAAvB,EAAoC;AACvC;AAEA,MAAIC,aAAJ;;AACA,MAAI;AACFA,IAAAA,aAAa,GAAGf,kBAAkB,CAAC,gBAAD,CAAlC;AACD,GAFD,CAEE,OAAMgB,KAAN,EAAa;AACb,QAAI,QAAOA,KAAP,MAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAAvC,IAA+CA,KAAK,CAACC,IAAN,KAAe,kBAAlE,EAAsF,CACpF;AACD,KAFD,MAEO;AACL,YAAMD,KAAN;AACD;AACF;;AAED,MAAID,aAAa;AACf;AACAA,EAAAA,aAAa,CAACG,UAAd,KAA6B,IAF/B,EAEqC;AACnC,QAAIA,UAAU,GAAIH,aAAa,CAACG,UAAhC;AACAf,IAAAA,MAAM,CAACU,IAAP,GAAcK,UAAU,CAACZ,WAAX,CAAuBa,IAAvB,CAA4BD,UAA5B,CAAd;AACAf,IAAAA,MAAM,CAACK,EAAP,GAAYU,UAAU,CAACV,EAAX,CAAcW,IAAd,CAAmBD,UAAnB,CAAZ;AACD,GAND,MAMO;AACLf,IAAAA,MAAM,CAACK,EAAP,GAAYM,OAAO,CAACN,EAAR,CAAWW,IAAX,CAAgBL,OAAhB,CAAZ;AACAX,IAAAA,MAAM,CAACU,IAAP,GAAcC,OAAO,CAACD,IAAR,CAAaM,IAAb,CAAkBL,OAAlB,CAAd,CAFK,CAGL;;AACAX,IAAAA,MAAM,CAACK,EAAP,CAAU,YAAV,EAAwB,YAAY;AAClCM,MAAAA,OAAO,CAACV,IAAR,CAAa,CAAb;AACD,KAFD;AAGAD,IAAAA,MAAM,CAACC,IAAP,GAAcU,OAAO,CAACV,IAAR,CAAae,IAAb,CAAkBL,OAAlB,CAAd;AACD;AACF,CA7BI,MA8BA;AACH,QAAM,IAAIM,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,SAASC,YAAT,CAAsBL,KAAtB,EAA6B;AAC3B,SAAOM,MAAM,CAACC,mBAAP,CAA2BP,KAA3B,EAAkCQ,MAAlC,CAAyC,UAASC,OAAT,EAAkBC,IAAlB,EAAwB;AACtE,WAAOJ,MAAM,CAACK,cAAP,CAAsBF,OAAtB,EAA+BC,IAA/B,EAAqC;AAC/CE,MAAAA,KAAK,EAAEZ,KAAK,CAACU,IAAD,CADmC;AAE/CG,MAAAA,UAAU,EAAE;AAFmC,KAArC,CAAP;AAID,GALM,EAKJ,EALI,CAAP;AAMD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,SAAT,CAAmBF,KAAnB,EAA0B;AACxB,SAAOA,KAAK,IAAK,OAAOA,KAAK,CAACG,IAAb,KAAsB,UAAhC,IAAgD,OAAOH,KAAK,SAAZ,KAAuB,UAA9E;AACD,EAED;;;AACAzB,MAAM,CAAC6B,OAAP,GAAiB,EAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA7B,MAAM,CAAC6B,OAAP,CAAeC,GAAf,GAAqB,SAASA,GAAT,CAAaC,EAAb,EAAiBC,IAAjB,EAAuB;AAC1C,MAAIC,CAAC,GAAG,IAAIC,QAAJ,CAAa,aAAaH,EAAb,GAAkB,2BAA/B,CAAR;AACA,SAAOE,CAAC,CAACE,KAAF,CAAQF,CAAR,EAAWD,IAAX,CAAP;AACD,CAHD;AAKA;AACA;AACA;AACA;;;AACAhC,MAAM,CAAC6B,OAAP,CAAeA,OAAf,GAAyB,SAASA,OAAT,GAAmB;AAC1C,SAAOV,MAAM,CAACiB,IAAP,CAAYpC,MAAM,CAAC6B,OAAnB,CAAP;AACD,CAFD;;AAIA,IAAIQ,gBAAgB,GAAG,IAAvB;AAEArC,MAAM,CAACK,EAAP,CAAU,SAAV,EAAqB,UAAUiC,OAAV,EAAmB;AACtC,MAAIA,OAAO,KAAKvC,mBAAhB,EAAqC;AACnC,WAAOC,MAAM,CAACC,IAAP,CAAY,CAAZ,CAAP;AACD;;AACD,MAAI;AACF,QAAIsC,MAAM,GAAGvC,MAAM,CAAC6B,OAAP,CAAeS,OAAO,CAACC,MAAvB,CAAb;;AAEA,QAAIA,MAAJ,EAAY;AACVF,MAAAA,gBAAgB,GAAGC,OAAO,CAACE,EAA3B,CADU,CAGV;;AACA,UAAIC,MAAM,GAAGF,MAAM,CAACJ,KAAP,CAAaI,MAAb,EAAqBD,OAAO,CAACI,MAA7B,CAAb;;AAEA,UAAIf,SAAS,CAACc,MAAD,CAAb,EAAuB;AACrB;AACAA,QAAAA,MAAM,CACDb,IADL,CACU,UAAUa,MAAV,EAAkB;AACtBzC,UAAAA,MAAM,CAACU,IAAP,CAAY;AACV8B,YAAAA,EAAE,EAAEF,OAAO,CAACE,EADF;AAEVC,YAAAA,MAAM,EAAEA,MAFE;AAGV5B,YAAAA,KAAK,EAAE;AAHG,WAAZ;AAKAwB,UAAAA,gBAAgB,GAAG,IAAnB;AACD,SARL,WASW,UAAUM,GAAV,EAAe;AACpB3C,UAAAA,MAAM,CAACU,IAAP,CAAY;AACV8B,YAAAA,EAAE,EAAEF,OAAO,CAACE,EADF;AAEVC,YAAAA,MAAM,EAAE,IAFE;AAGV5B,YAAAA,KAAK,EAAEK,YAAY,CAACyB,GAAD;AAHT,WAAZ;AAKAN,UAAAA,gBAAgB,GAAG,IAAnB;AACD,SAhBL;AAiBD,OAnBD,MAoBK;AACH;AACArC,QAAAA,MAAM,CAACU,IAAP,CAAY;AACV8B,UAAAA,EAAE,EAAEF,OAAO,CAACE,EADF;AAEVC,UAAAA,MAAM,EAAEA,MAFE;AAGV5B,UAAAA,KAAK,EAAE;AAHG,SAAZ;AAMAwB,QAAAA,gBAAgB,GAAG,IAAnB;AACD;AACF,KApCD,MAqCK;AACH,YAAM,IAAIpB,KAAJ,CAAU,qBAAqBqB,OAAO,CAACC,MAA7B,GAAsC,GAAhD,CAAN;AACD;AACF,GA3CD,CA4CA,OAAOI,GAAP,EAAY;AACV3C,IAAAA,MAAM,CAACU,IAAP,CAAY;AACV8B,MAAAA,EAAE,EAAEF,OAAO,CAACE,EADF;AAEVC,MAAAA,MAAM,EAAE,IAFE;AAGV5B,MAAAA,KAAK,EAAEK,YAAY,CAACyB,GAAD;AAHT,KAAZ;AAKD;AACF,CAvDD;AAyDA;AACA;AACA;AACA;;AACA3C,MAAM,CAAC4C,QAAP,GAAkB,UAAUf,OAAV,EAAmB;AAEnC,MAAIA,OAAJ,EAAa;AACX,SAAK,IAAIN,IAAT,IAAiBM,OAAjB,EAA0B;AACxB,UAAIA,OAAO,CAACgB,cAAR,CAAuBtB,IAAvB,CAAJ,EAAkC;AAChCvB,QAAAA,MAAM,CAAC6B,OAAP,CAAeN,IAAf,IAAuBM,OAAO,CAACN,IAAD,CAA9B;AACD;AACF;AACF;;AAEDvB,EAAAA,MAAM,CAACU,IAAP,CAAY,OAAZ;AAED,CAZD;;AAcAV,MAAM,CAAC8C,IAAP,GAAc,UAAUC,OAAV,EAAmB;AAC/B,MAAIV,gBAAJ,EAAsB;AACpBrC,IAAAA,MAAM,CAACU,IAAP,CAAY;AACV8B,MAAAA,EAAE,EAAEH,gBADM;AAEVW,MAAAA,OAAO,EAAE,IAFC;AAGVD,MAAAA,OAAO,EAAPA;AAHU,KAAZ;AAKD;AACF,CARD;;AAUA,IAAI,IAAJ,EAAoC;AAClCE,EAAAA,yBAAA,GAAcjD,MAAM,CAAC4C,QAArB;AACAK,EAAAA,yBAAA,GAAejD,MAAM,CAAC8C,IAAtB;AACD", "sources": ["webpack://workerpool/./src/worker.js"], "sourcesContent": ["/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n  } else {\n    worker.on = process.on.bind(process);\n    worker.send = process.send.bind(process);\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.exit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              worker.send({\n                id: request.id,\n                result: result,\n                error: null\n              });\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        worker.send({\n          id: request.id,\n          result: result,\n          error: null\n        });\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} methods\n */\nworker.register = function (methods) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  worker.send('ready');\n\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n"], "names": ["requireFoolWebpack", "eval", "TERMINATE_METHOD_ID", "worker", "exit", "self", "postMessage", "addEventListener", "on", "event", "callback", "message", "data", "send", "process", "WorkerThreads", "error", "code", "parentPort", "bind", "Error", "convertError", "Object", "getOwnPropertyNames", "reduce", "product", "name", "defineProperty", "value", "enumerable", "isPromise", "then", "methods", "run", "fn", "args", "f", "Function", "apply", "keys", "currentRequestId", "request", "method", "id", "result", "params", "err", "register", "hasOwnProperty", "emit", "payload", "isEvent", "exports", "add"], "sourceRoot": ""}