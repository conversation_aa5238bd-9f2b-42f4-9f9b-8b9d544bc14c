<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetromino - Kotlin/WASM</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #333;
            background-color: #000;
        }

        .controls {
            margin-top: 20px;
            font-size: 14px;
        }

        .score {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .console-output {
            margin-top: 20px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #444;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Tetromino - Kotlin/WASM</h1>

        <div class="status">
            <div>Status: <span id="status">Loading...</span></div>
            <div>Score: <span id="score">0</span></div>
            <div>Lines: <span id="lines">0</span></div>
            <div>Level: <span id="level">1</span></div>
        </div>

        <canvas id="gameCanvas" width="400" height="600"></canvas>

        <div class="controls">
            <p><strong>Controls:</strong></p>
            <p>← → : Move left/right</p>
            <p>↓ : Soft drop</p>
            <p>↑ : Rotate</p>
            <p>Space : Hard drop</p>
            <p>P : Pause</p>
            <p>
                <button onclick="startDemo()">Start Demo</button>
                <button onclick="debugGame()">Debug Game</button>
                <button onclick="renderConsole()">Render Console</button>
            </p>
        </div>

        <div class="console-output" id="console">
            Console output will appear here...
        </div>
    </div>

    <script>
        // 重定向 console.log 到页面
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.textContent += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };

        // 游戏实例
        let game = null;
        let renderer = null;
        let gameLoop = null;

        // 启动演示
        function startDemo() {
            console.log('Starting Tetromino demo...');
            document.getElementById('status').textContent = 'Demo Running';

            // 创建一个简单的演示
            createSimpleDemo();
        }

        // 创建简单演示
        function createSimpleDemo() {
            const renderer = createCanvasRenderer();

            // 创建一个模拟的游戏状态
            const demoGameState = {
                status: 'PLAYING',
                score: 1200,
                level: 2,
                lines: 12,
                boardCells: [
                    // 底部一些固定方块
                    {x: 0, y: 19}, {x: 1, y: 19}, {x: 2, y: 19}, {x: 4, y: 19}, {x: 5, y: 19}, {x: 6, y: 19}, {x: 7, y: 19}, {x: 8, y: 19}, {x: 9, y: 19},
                    {x: 0, y: 18}, {x: 2, y: 18}, {x: 3, y: 18}, {x: 5, y: 18}, {x: 7, y: 18}, {x: 8, y: 18}, {x: 9, y: 18},
                    {x: 1, y: 17}, {x: 3, y: 17}, {x: 4, y: 17}, {x: 6, y: 17}, {x: 8, y: 17}, {x: 9, y: 17}
                ],
                activeTetromino: [
                    // T 型方块在中间位置
                    {x: 4, y: 2, type: 'T'},
                    {x: 3, y: 3, type: 'T'},
                    {x: 4, y: 3, type: 'T'},
                    {x: 5, y: 3, type: 'T'}
                ]
            };

            // 渲染演示状态
            renderer.render(demoGameState);

            // 模拟方块下落动画
            let currentY = 2;
            const animationInterval = setInterval(() => {
                currentY++;
                if (currentY > 15) {
                    clearInterval(animationInterval);
                    return;
                }

                // 更新活动方块位置
                demoGameState.activeTetromino = [
                    {x: 4, y: currentY, type: 'T'},
                    {x: 3, y: currentY + 1, type: 'T'},
                    {x: 4, y: currentY + 1, type: 'T'},
                    {x: 5, y: currentY + 1, type: 'T'}
                ];

                renderer.render(demoGameState);
            }, 300);
        }

        // 调试游戏
        function debugGame() {
            console.log('Debug: WASM module should be loaded');
            console.log('Available functions:', Object.keys(window).filter(key => key.includes('Game') || key.includes('game')));
        }

        // 渲染到控制台
        function renderConsole() {
            console.log('Console render: Check browser console for WASM output');
        }

        // 这些函数已经被简化的演示替代

        // 简单的 Canvas 渲染器
        function createCanvasRenderer() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const cellSize = 20;

            return {
                render: function(gameState) {
                    // 清空画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 绘制游戏板边框
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(0, 0, 200, 400);

                    // 绘制固定方块
                    if (gameState.boardCells) {
                        gameState.boardCells.forEach(cell => {
                            drawCell(ctx, cell, '#666', cellSize);
                        });
                    }

                    // 绘制活动方块
                    if (gameState.activeTetromino) {
                        gameState.activeTetromino.forEach(cell => {
                            const color = getTetrominoColor(cell.type);
                            drawCell(ctx, cell, color, cellSize);
                        });
                    }

                    // 更新 UI
                    document.getElementById('score').textContent = gameState.score || 0;
                    document.getElementById('lines').textContent = gameState.lines || 0;
                    document.getElementById('level').textContent = gameState.level || 1;
                    document.getElementById('status').textContent = gameState.status || 'Unknown';

                    // 绘制游戏状态覆盖层
                    if (gameState.status === 'GAME_OVER') {
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                        ctx.fillRect(0, 0, 200, 400);

                        ctx.fillStyle = '#fff';
                        ctx.font = '24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('GAME OVER', 100, 200);
                    } else if (gameState.status === 'PAUSED') {
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                        ctx.fillRect(0, 0, 200, 400);

                        ctx.fillStyle = '#fff';
                        ctx.font = '24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('PAUSED', 100, 200);
                    }
                }
            };
        }

        function drawCell(ctx, cell, color, cellSize) {
            const x = cell.x * cellSize;
            const y = cell.y * cellSize;

            ctx.fillStyle = color;
            ctx.fillRect(x, y, cellSize, cellSize);

            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, cellSize, cellSize);
        }

        function getTetrominoColor(type) {
            switch(type) {
                case 'I': return '#00f0f0';
                case 'O': return '#f0f000';
                case 'T': return '#a000f0';
                case 'S': return '#00f000';
                case 'Z': return '#f00000';
                case 'J': return '#0000f0';
                case 'L': return '#f0a000';
                default: return '#666';
            }
        }

        // 简化的键盘事件处理（仅用于演示）
        document.addEventListener('keydown', function(event) {
            console.log('Key pressed:', event.code);
            // 在演示模式下，键盘输入只是显示日志
        });
    </script>

    <script src="tetromino.js"></script>
</body>
</html>
