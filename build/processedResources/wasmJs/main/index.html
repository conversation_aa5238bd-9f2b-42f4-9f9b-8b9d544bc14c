<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetromino - Kotlin/WASM</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #333;
            background-color: #000;
        }

        .controls {
            margin-top: 20px;
            font-size: 14px;
        }

        .score {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .console-output {
            margin-top: 20px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #444;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Tetromino - Kotlin/WASM</h1>

        <div class="status">
            <div>Status: <span id="status">Loading...</span></div>
            <div>Score: <span id="score">0</span></div>
            <div>Lines: <span id="lines">0</span></div>
            <div>Level: <span id="level">1</span></div>
        </div>

        <canvas id="gameCanvas" width="400" height="600"></canvas>

        <div class="controls">
            <p><strong>Controls:</strong></p>
            <p>← → : Move left/right</p>
            <p>↓ : Soft drop</p>
            <p>↑ : Rotate</p>
            <p>Space : Hard drop</p>
            <p>P : Pause</p>
            <p>
                <button onclick="startDemo()">Start Demo</button>
                <button onclick="debugGame()">Debug Game</button>
                <button onclick="renderConsole()">Render Console</button>
            </p>
        </div>

        <div class="console-output" id="console">
            Console output will appear here...
        </div>
    </div>

    <script>
        // 重定向 console.log 到页面
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.textContent += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };

        // 游戏实例
        let game = null;
        let renderer = null;
        let gameLoop = null;

        // 启动演示
        function startDemo() {
            console.log('Starting Tetromino demo...');
            document.getElementById('status').textContent = 'Demo Running';

            // 等待 WASM 模块加载完成
            setTimeout(() => {
                initializeGame();
            }, 1000);
        }

        // 调试游戏
        function debugGame() {
            try {
                if (typeof getGameState === 'function') {
                    const gameStateJson = getGameState();
                    console.log('Game State JSON:', gameStateJson);
                    const gameState = JSON.parse(gameStateJson);
                    console.log('Parsed Game State:', gameState);
                } else {
                    console.log('getGameState function not available');
                }
            } catch (error) {
                console.error('Debug error:', error);
            }
        }

        // 渲染到控制台
        function renderConsole() {
            try {
                if (typeof renderToConsole === 'function') {
                    const result = renderToConsole();
                    console.log('Render result:', result);
                } else {
                    console.log('renderToConsole function not available');
                }
            } catch (error) {
                console.error('Render error:', error);
            }
        }

        // 初始化游戏
        function initializeGame() {
            try {
                // 调用 WASM 函数创建游戏
                if (typeof createGame === 'function') {
                    const result = createGame();
                    console.log('Game creation result:', result);

                    // 创建渲染器
                    renderer = createCanvasRenderer();

                    // 开始游戏循环
                    startGameLoop();

                    document.getElementById('status').textContent = 'Game Running';
                } else {
                    console.error('WASM functions not available yet');
                    document.getElementById('status').textContent = 'WASM Loading...';
                    // 重试
                    setTimeout(initializeGame, 500);
                }
            } catch (error) {
                console.error('Error initializing game:', error);
                document.getElementById('status').textContent = 'Error: ' + error.message;
            }
        }

        // 游戏循环
        function startGameLoop() {
            if (gameLoop) {
                clearInterval(gameLoop);
            }

            gameLoop = setInterval(() => {
                try {
                    // 更新游戏状态
                    if (typeof updateGame === 'function') {
                        updateGame();
                    }

                    // 获取游戏状态并渲染
                    if (typeof getGameState === 'function' && renderer) {
                        const gameStateJson = getGameState();
                        const gameState = JSON.parse(gameStateJson);
                        renderer.render(gameState);
                    }
                } catch (error) {
                    console.error('Game loop error:', error);
                }
            }, 500); // 每500ms更新一次
        }

        // 简单的 Canvas 渲染器
        function createCanvasRenderer() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const cellSize = 20;

            return {
                render: function(gameState) {
                    // 清空画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 绘制游戏板边框
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(0, 0, 200, 400);

                    // 绘制固定方块
                    if (gameState.boardCells) {
                        gameState.boardCells.forEach(cell => {
                            drawCell(ctx, cell, '#666', cellSize);
                        });
                    }

                    // 绘制活动方块
                    if (gameState.activeTetromino) {
                        gameState.activeTetromino.forEach(cell => {
                            const color = getTetrominoColor(cell.type);
                            drawCell(ctx, cell, color, cellSize);
                        });
                    }

                    // 更新 UI
                    document.getElementById('score').textContent = gameState.score || 0;
                    document.getElementById('lines').textContent = gameState.lines || 0;
                    document.getElementById('level').textContent = gameState.level || 1;
                    document.getElementById('status').textContent = gameState.status || 'Unknown';

                    // 绘制游戏状态覆盖层
                    if (gameState.status === 'GAME_OVER') {
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                        ctx.fillRect(0, 0, 200, 400);

                        ctx.fillStyle = '#fff';
                        ctx.font = '24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('GAME OVER', 100, 200);
                    } else if (gameState.status === 'PAUSED') {
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                        ctx.fillRect(0, 0, 200, 400);

                        ctx.fillStyle = '#fff';
                        ctx.font = '24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('PAUSED', 100, 200);
                    }
                }
            };
        }

        function drawCell(ctx, cell, color, cellSize) {
            const x = cell.x * cellSize;
            const y = cell.y * cellSize;

            ctx.fillStyle = color;
            ctx.fillRect(x, y, cellSize, cellSize);

            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, cellSize, cellSize);
        }

        function getTetrominoColor(type) {
            switch(type) {
                case 'I': return '#00f0f0';
                case 'O': return '#f0f000';
                case 'T': return '#a000f0';
                case 'S': return '#00f000';
                case 'Z': return '#f00000';
                case 'J': return '#0000f0';
                case 'L': return '#f0a000';
                default: return '#666';
            }
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            let action = null;
            switch(event.code) {
                case 'ArrowLeft': action = 'MOVE_LEFT'; break;
                case 'ArrowRight': action = 'MOVE_RIGHT'; break;
                case 'ArrowDown': action = 'SOFT_DROP'; break;
                case 'ArrowUp': action = 'ROTATE'; break;
                case 'Space': action = 'HARD_DROP'; break;
                case 'KeyP': action = 'PAUSE'; break;
            }

            if (action) {
                try {
                    if (typeof handleInput === 'function') {
                        const result = handleInput(action);
                        console.log('Input action:', action, 'Result:', result);
                    } else {
                        console.log('handleInput function not available');
                    }
                } catch (error) {
                    console.error('Error handling input:', error);
                }
                event.preventDefault();
            }
        });
    </script>

    <script src="tetromino.js"></script>
</body>
</html>
