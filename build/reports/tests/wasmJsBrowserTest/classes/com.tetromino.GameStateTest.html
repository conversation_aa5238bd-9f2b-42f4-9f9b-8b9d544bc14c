<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - GameStateTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>GameStateTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.tetromino.html">com.tetromino</a> &gt; GameStateTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Method name</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">shouldCalculateScoreForClearedLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldCalculateScoreForClearedLines</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldCalculateScoreForMultipleLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldCalculateScoreForMultipleLines</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldCreateGameStateWithInitialValues[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldCreateGameStateWithInitialValues</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldEndGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldEndGame</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldIncreaseLevelBasedOnLinesCleared[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldIncreaseLevelBasedOnLinesCleared</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldPauseGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldPauseGame</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldResumeGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldResumeGame</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldStartGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldStartGame</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at 2025年6月26日 17:42:27</p>
</div>
</div>
</body>
</html>
