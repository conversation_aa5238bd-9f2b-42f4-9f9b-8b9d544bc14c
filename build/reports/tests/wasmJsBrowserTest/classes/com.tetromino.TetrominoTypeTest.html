<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - TetrominoTypeTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>TetrominoTypeTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.tetromino.html">com.tetromino</a> &gt; TetrominoTypeTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Method name</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">shouldHaveCorrectShapeForIType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldHaveCorrectShapeForIType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldHaveCorrectShapeForOType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldHaveCorrectShapeForOType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldHaveCorrectShapeForTType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldHaveCorrectShapeForTType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldHaveSevenTetrominoTypes[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]</td>
<td class="success">shouldHaveSevenTetrominoTypes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at 2025年6月26日 17:42:27</p>
</div>
</div>
</body>
</html>
