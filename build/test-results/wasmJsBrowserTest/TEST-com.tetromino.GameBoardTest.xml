<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.GameBoardTest" tests="10" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:16:36.201Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="shouldCreateEmptyBoardWithCorrectDimensions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldValidatePositionsWithinBounds[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldSetAndCheckOccupiedPositions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldReturnFalseForOccupiedCheckOnInvalidPositions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldDetectCollisionWithOccupiedCells[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldDetectCollisionWithBoundaries[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldNotDetectCollisionForValidPosition[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldPlaceTetrominoOnBoard[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldDetectFullLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldClearFullLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
