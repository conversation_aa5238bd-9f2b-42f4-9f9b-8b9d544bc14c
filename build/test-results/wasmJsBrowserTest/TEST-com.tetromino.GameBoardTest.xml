<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.GameBoardTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-06-26T08:00:41.013Z" hostname="zxnapdeMacBook-Pro.local" time="0.003">
  <properties/>
  <testcase name="shouldCreateEmptyBoardWithCorrectDimensions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.001"/>
  <testcase name="shouldValidatePositionsWithinBounds[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldSetAndCheckOccupiedPositions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <testcase name="shouldReturnFalseForOccupiedCheckOnInvalidPositions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameBoardTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
