<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.GameStateTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:53:35.474Z" hostname="zxnapdeMacBook-Pro.local" time="0.001">
  <properties/>
  <testcase name="shouldCreateGameStateWithInitialValues[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldStartGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldPauseGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.001"/>
  <testcase name="shouldResumeGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldEndGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldCalculateScoreForClearedLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldCalculateScoreForMultipleLines[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <testcase name="shouldIncreaseLevelBasedOnLinesCleared[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.GameStateTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
