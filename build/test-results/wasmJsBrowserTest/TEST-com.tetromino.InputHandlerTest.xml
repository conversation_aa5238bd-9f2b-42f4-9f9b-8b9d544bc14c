<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.InputHandlerTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:42:27.657Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="shouldMoveTetrominoLeft[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldMoveTetrominoRight[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldMoveTetrominoDown[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldRotateTetrominoClockwise[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldNotMoveIfCollisionDetected[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldNotRotateIfCollisionDetected[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <testcase name="shouldHardDropTetromino[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.InputHandlerTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
