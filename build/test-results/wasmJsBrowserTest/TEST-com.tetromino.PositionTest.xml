<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.PositionTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-26T08:51:50.091Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="shouldCreatePositionWithCorrectCoordinates[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.PositionTest" time="0.0"/>
  <testcase name="shouldSupportNegativeCoordinates[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.PositionTest" time="0.0"/>
  <testcase name="shouldImplementEqualityCorrectly[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.PositionTest" time="0.0"/>
  <testcase name="shouldAddPositionsCorrectly[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.PositionTest" time="0.0"/>
  <testcase name="shouldSubtractPositionsCorrectly[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.PositionTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
