<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.RendererTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:42:27.657Z" hostname="zxnapdeMacBook-Pro.local" time="0.001">
  <properties/>
  <testcase name="shouldCreateRendererWithCorrectDimensions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.0"/>
  <testcase name="shouldCalculateCorrectBoardDimensions[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.001"/>
  <testcase name="shouldCalculateCorrectCellPosition[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.0"/>
  <testcase name="shouldCreateRenderData[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.0"/>
  <testcase name="shouldConvertBoardToRenderCells[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.0"/>
  <testcase name="shouldConvertTetrominoToRenderCells[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.RendererTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
