<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.TetrisGameTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:53:35.476Z" hostname="zxnapdeMacBook-Pro.local" time="0.001">
  <properties/>
  <testcase name="shouldCreateGameWithInitialState[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldStartGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldPauseAndResumeGame[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldHandleUserInput[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldSpawnNewTetrominoWhenCurrentLands[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldClearLinesAndUpdateScore[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldDetectGameOver[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <testcase name="shouldGenerateRenderData[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrisGameTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
