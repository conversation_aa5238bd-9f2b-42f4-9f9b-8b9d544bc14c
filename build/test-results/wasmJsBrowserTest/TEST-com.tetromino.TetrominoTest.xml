<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.TetrominoTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:16:36.202Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="shouldCreateTetrominoWithCorrectProperties[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <testcase name="shouldGetAbsolutePositionsCorrectly[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <testcase name="shouldMoveCorrectly[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <testcase name="shouldRotateClockwise[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <testcase name="shouldRotateCounterClockwise[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <testcase name="shouldHandleRotationWrapAround[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
