<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tetromino.TetrominoTypeTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-06-26T09:42:27.658Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="shouldHaveSevenTetrominoTypes[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTypeTest" time="0.0"/>
  <testcase name="shouldHaveCorrectShapeForIType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTypeTest" time="0.0"/>
  <testcase name="shouldHaveCorrectShapeForOType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTypeTest" time="0.0"/>
  <testcase name="shouldHaveCorrectShapeForTType[wasmJs, browser, ChromeHeadless137.0.0.0, MacOS10.15.7]" classname="com.tetromino.TetrominoTypeTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
