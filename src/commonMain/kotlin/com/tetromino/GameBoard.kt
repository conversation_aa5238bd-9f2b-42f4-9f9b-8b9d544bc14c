package com.tetromino

/**
 * 游戏板，管理游戏区域的状态
 * @param width 游戏板宽度（格子数）
 * @param height 游戏板高度（格子数）
 */
class GameBoard(val width: Int, val height: Int) {
    
    // 使用二维数组存储游戏板状态，true 表示被占用
    private val board = Array(height) { BooleanArray(width) }
    
    /**
     * 检查位置是否在游戏板范围内
     */
    fun isValidPosition(position: Position): Boolean {
        return position.x >= 0 && position.x < width && 
               position.y >= 0 && position.y < height
    }
    
    /**
     * 检查位置是否被占用
     * 对于无效位置返回 false
     */
    fun isOccupied(position: Position): Bo<PERSON>an {
        if (!isValidPosition(position)) {
            return false
        }
        return board[position.y][position.x]
    }
    
    /**
     * 设置位置的占用状态
     * 只对有效位置进行操作
     */
    fun setOccupied(position: Position, occupied: Boolean) {
        if (isValidPosition(position)) {
            board[position.y][position.x] = occupied
        }
    }

    /**
     * 检测方块是否与游戏板发生碰撞
     * 包括与已占用位置的碰撞和边界碰撞
     */
    fun hasCollision(tetromino: Tetromino): Boolean {
        val absolutePositions = tetromino.getAbsolutePositions()

        return absolutePositions.any { position ->
            // 检查边界碰撞
            !isValidPosition(position) ||
            // 检查与已占用位置的碰撞
            isOccupied(position)
        }
    }

    /**
     * 将方块放置到游戏板上
     */
    fun placeTetromino(tetromino: Tetromino) {
        val absolutePositions = tetromino.getAbsolutePositions()
        absolutePositions.forEach { position ->
            setOccupied(position, true)
        }
    }

    /**
     * 获取所有满行的行号
     */
    fun getFullLines(): List<Int> {
        val fullLines = mutableListOf<Int>()

        for (y in 0 until height) {
            var isFull = true
            for (x in 0 until width) {
                if (!board[y][x]) {
                    isFull = false
                    break
                }
            }
            if (isFull) {
                fullLines.add(y)
            }
        }

        return fullLines
    }

    /**
     * 清除满行并让上面的方块下落
     * @return 清除的行数
     */
    fun clearFullLines(): Int {
        val fullLines = getFullLines()
        if (fullLines.isEmpty()) {
            return 0
        }

        // 从下往上逐行清除满行
        fullLines.sortedDescending().forEach { fullLineY ->
            // 将满行上方的所有行下移一行
            for (y in fullLineY downTo 1) {
                for (x in 0 until width) {
                    board[y][x] = board[y - 1][x]
                }
            }
            // 清空最顶行
            for (x in 0 until width) {
                board[0][x] = false
            }
        }

        return fullLines.size
    }
}
