package com.tetromino

/**
 * 游戏状态管理类
 * @param status 当前游戏状态
 * @param score 当前分数
 * @param linesCleared 已清除的行数
 * @param level 当前等级
 */
data class GameState(
    val status: GameStatus = GameStatus.NOT_STARTED,
    val score: Int = 0,
    val linesCleared: Int = 0,
    val level: Int = 1
) {
    
    /**
     * 游戏是否结束
     */
    val isGameOver: Boolean
        get() = status == GameStatus.GAME_OVER
    
    /**
     * 开始游戏
     */
    fun start(): GameState {
        return copy(status = GameStatus.PLAYING)
    }
    
    /**
     * 暂停游戏
     */
    fun pause(): GameState {
        return if (status == GameStatus.PLAYING) {
            copy(status = GameStatus.PAUSED)
        } else {
            this
        }
    }
    
    /**
     * 恢复游戏
     */
    fun resume(): GameState {
        return if (status == GameStatus.PAUSED) {
            copy(status = GameStatus.PLAYING)
        } else {
            this
        }
    }
    
    /**
     * 游戏结束
     */
    fun gameOver(): GameState {
        return copy(status = GameStatus.GAME_OVER)
    }
    
    /**
     * 添加分数（基于清除的行数）
     * @param clearedLines 清除的行数
     */
    fun addScore(clearedLines: Int): GameState {
        if (clearedLines <= 0) return this
        
        // 分数计算：1行=100分，2行=300分，3行=500分，4行=800分
        val scoreToAdd = when (clearedLines) {
            1 -> 100
            2 -> 300
            3 -> 500
            4 -> 800
            else -> clearedLines * 100 // 超过4行按每行100分计算
        }
        
        val newLinesCleared = linesCleared + clearedLines
        val newScore = score + scoreToAdd
        val newLevel = calculateLevel(newLinesCleared)
        
        return copy(
            score = newScore,
            linesCleared = newLinesCleared,
            level = newLevel
        )
    }
    
    /**
     * 根据清除的行数计算等级
     * 每10行升一级
     */
    private fun calculateLevel(totalLinesCleared: Int): Int {
        return (totalLinesCleared / 10) + 1
    }
}
