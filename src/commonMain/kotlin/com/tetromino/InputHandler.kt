package com.tetromino

/**
 * 输入处理器，处理用户的各种输入操作
 */
class InputHandler {
    
    /**
     * 处理输入动作
     * @param action 输入动作
     * @param tetromino 当前方块
     * @param board 游戏板
     * @return 处理后的方块状态
     */
    fun handleInput(action: InputAction, tetromino: Tetromino, board: GameBoard): Tetromino {
        return when (action) {
            InputAction.MOVE_LEFT -> moveIfPossible(tetromino, Position(-1, 0), board)
            InputAction.MOVE_RIGHT -> moveIfPossible(tetromino, Position(1, 0), board)
            InputAction.SOFT_DROP -> moveIfPossible(tetromino, Position(0, 1), board)
            InputAction.ROTATE -> rotateIfPossible(tetromino, board)
            InputAction.HARD_DROP -> hardDrop(tetromino, board)
            InputAction.PAUSE -> tetromino // 暂停不改变方块状态
        }
    }
    
    /**
     * 如果可能的话移动方块
     */
    private fun moveIfPossible(tetromino: Tetromino, offset: Position, board: GameBoard): Tetromino {
        val newTetromino = tetromino.move(offset)
        return if (board.hasCollision(newTetromino)) {
            tetromino // 有碰撞，保持原位置
        } else {
            newTetromino // 无碰撞，返回新位置
        }
    }
    
    /**
     * 如果可能的话旋转方块
     */
    private fun rotateIfPossible(tetromino: Tetromino, board: GameBoard): Tetromino {
        val rotatedTetromino = tetromino.rotateClockwise()
        return if (board.hasCollision(rotatedTetromino)) {
            tetromino // 有碰撞，保持原旋转状态
        } else {
            rotatedTetromino // 无碰撞，返回旋转后的状态
        }
    }
    
    /**
     * 硬下落：将方块瞬间移动到最底部可能的位置
     */
    private fun hardDrop(tetromino: Tetromino, board: GameBoard): Tetromino {
        var currentTetromino = tetromino
        
        // 持续向下移动直到碰撞
        while (true) {
            val nextTetromino = currentTetromino.move(Position(0, 1))
            if (board.hasCollision(nextTetromino)) {
                break // 下一步会碰撞，停在当前位置
            }
            currentTetromino = nextTetromino
        }
        
        return currentTetromino
    }
}
