package com.tetromino

/**
 * 渲染数据，包含所有需要渲染的信息
 */
data class RenderData(
    val boardCells: List<RenderCell>,      // 游戏板上的固定方块
    val activeTetromino: List<RenderCell>, // 当前活动的方块
    val score: Int,                        // 分数
    val level: Int,                        // 等级
    val linesCleared: Int,                 // 已清除行数
    val gameStatus: GameStatus             // 游戏状态
)

/**
 * 渲染单元格，表示一个需要渲染的方块格子
 */
data class RenderCell(
    val position: Position,                    // 位置
    val tetrominoType: TetrominoType? = null  // 方块类型（用于确定颜色）
)
