package com.tetromino

/**
 * 渲染器，负责将游戏状态转换为渲染数据
 * @param canvasWidth Canvas 宽度
 * @param canvasHeight Canvas 高度
 * @param cellSize 每个格子的像素大小
 */
class Renderer(
    val canvasWidth: Int,
    val canvasHeight: Int,
    val cellSize: Int
) {
    
    /**
     * 创建渲染数据
     */
    fun createRenderData(
        board: GameBoard,
        activeTetromino: Tetromino?,
        gameState: GameState
    ): RenderData {
        val boardCells = convertBoardToRenderCells(board)
        val tetrominoCells = activeTetromino?.let { convertTetrominoToRenderCells(it) } ?: emptyList()
        
        return RenderData(
            boardCells = boardCells,
            activeTetromino = tetrominoCells,
            score = gameState.score,
            level = gameState.level,
            linesCleared = gameState.linesCleared,
            gameStatus = gameState.status
        )
    }
    
    /**
     * 将游戏板转换为渲染单元格
     */
    fun convertBoardToRenderCells(board: GameBoard): List<RenderCell> {
        val cells = mutableListOf<RenderCell>()
        
        for (y in 0 until board.height) {
            for (x in 0 until board.width) {
                val position = Position(x, y)
                if (board.isOccupied(position)) {
                    cells.add(RenderCell(position, null)) // 固定方块没有特定类型
                }
            }
        }
        
        return cells
    }
    
    /**
     * 将方块转换为渲染单元格
     */
    fun convertTetrominoToRenderCells(tetromino: Tetromino): List<RenderCell> {
        val absolutePositions = tetromino.getAbsolutePositions()
        return absolutePositions.map { position ->
            RenderCell(position, tetromino.type)
        }
    }
    
    /**
     * 计算游戏板的像素宽度
     */
    fun calculateBoardWidth(board: GameBoard): Int {
        return board.width * cellSize
    }
    
    /**
     * 计算游戏板的像素高度
     */
    fun calculateBoardHeight(board: GameBoard): Int {
        return board.height * cellSize
    }
    
    /**
     * 将游戏坐标转换为像素坐标
     */
    fun calculatePixelPosition(position: Position): Position {
        return Position(
            position.x * cellSize,
            position.y * cellSize
        )
    }
}
