package com.tetromino

/**
 * 俄罗斯方块游戏主控制器
 * @param boardWidth 游戏板宽度
 * @param boardHeight 游戏板高度
 */
class TetrisGame(
    boardWidth: Int = 10,
    boardHeight: Int = 20
) {
    val board = GameBoard(boardWidth, boardHeight)
    private val inputHandler = InputHandler()
    private val renderer = Renderer(400, 600, 20)

    var gameState = GameState()
        private set

    var currentTetromino: Tetromino? = null
        private set

    private var nextTetromino: Tetromino? = null

    // 下落计数器，用于控制自动下落速度
    private var dropCounter = 0
    private val dropInterval = 5 // 每5次update调用下落一次

    init {
        spawnNewTetromino()
    }
    
    /**
     * 开始游戏
     */
    fun start() {
        gameState = gameState.start()
        spawnNewTetromino()
    }
    
    /**
     * 暂停游戏
     */
    fun pause() {
        gameState = gameState.pause()
    }
    
    /**
     * 恢复游戏
     */
    fun resume() {
        gameState = gameState.resume()
    }
    
    /**
     * 处理用户输入
     */
    fun handleInput(action: InputAction) {
        if (gameState.status != GameStatus.PLAYING) return
        
        currentTetromino?.let { tetromino ->
            when (action) {
                InputAction.PAUSE -> pause()
                else -> {
                    val newTetromino = inputHandler.handleInput(action, tetromino, board)
                    currentTetromino = newTetromino
                    
                    // 如果是硬下落，立即处理着陆
                    if (action == InputAction.HARD_DROP) {
                        handleTetrominoLanding()
                    }
                }
            }
        }
    }
    
    /**
     * 游戏更新（每帧调用）
     */
    fun update() {
        if (gameState.status != GameStatus.PLAYING) return
        
        // 检查游戏结束条件
        if (isGameOver()) {
            gameState = gameState.gameOver()
            return
        }
        
        // 清除满行
        val clearedLines = board.clearFullLines()
        if (clearedLines > 0) {
            gameState = gameState.addScore(clearedLines)
        }
        
        // 自动下落逻辑（控制下落速度）
        dropCounter++
        if (dropCounter >= dropInterval) {
            dropCounter = 0
            currentTetromino?.let { tetromino ->
                val downTetromino = tetromino.move(Position(0, 1))
                if (board.hasCollision(downTetromino)) {
                    handleTetrominoLanding()
                } else {
                    currentTetromino = downTetromino
                }
            }
        }
    }
    
    /**
     * 获取渲染数据
     */
    fun getRenderData(): RenderData {
        return renderer.createRenderData(board, currentTetromino, gameState)
    }
    
    /**
     * 生成新方块
     */
    private fun spawnNewTetromino() {
        val types = TetrominoType.values()
        val randomType = types.random()
        val spawnPosition = Position(board.width / 2 - 1, 0)
        
        currentTetromino = Tetromino(randomType, spawnPosition)
    }
    
    /**
     * 处理方块着陆
     */
    private fun handleTetrominoLanding() {
        currentTetromino?.let { tetromino ->
            board.placeTetromino(tetromino)
            spawnNewTetromino()
        }
    }
    
    /**
     * 检查游戏是否结束
     */
    private fun isGameOver(): Boolean {
        currentTetromino?.let { tetromino ->
            return board.hasCollision(tetromino)
        }
        return false
    }
}
