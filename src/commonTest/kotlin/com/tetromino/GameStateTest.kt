package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * 测试 GameState 的基本功能
 * 理由：GameState 管理游戏的整体状态，需要验证状态转换、分数计算等核心业务逻辑
 */
class GameStateTest {

    @Test
    fun shouldCreateGameStateWithInitialValues() {
        // Arrange & Act
        val gameState = GameState()
        
        // Assert
        assertEquals(GameStatus.NOT_STARTED, gameState.status)
        assertEquals(0, gameState.score)
        assertEquals(0, gameState.linesCleared)
        assertEquals(1, gameState.level)
        assertFalse(gameState.isGameOver)
    }

    @Test
    fun shouldStartGame() {
        // Arrange
        val gameState = GameState()
        
        // Act
        val newState = gameState.start()
        
        // Assert
        assertEquals(GameStatus.PLAYING, newState.status)
        assertEquals(0, newState.score)
        assertEquals(1, newState.level)
    }

    @Test
    fun shouldPauseGame() {
        // Arrange
        val gameState = GameState().start()
        
        // Act
        val pausedState = gameState.pause()
        
        // Assert
        assertEquals(GameStatus.PAUSED, pausedState.status)
    }

    @Test
    fun shouldResumeGame() {
        // Arrange
        val gameState = GameState().start().pause()
        
        // Act
        val resumedState = gameState.resume()
        
        // Assert
        assertEquals(GameStatus.PLAYING, resumedState.status)
    }

    @Test
    fun shouldEndGame() {
        // Arrange
        val gameState = GameState().start()
        
        // Act
        val endedState = gameState.gameOver()
        
        // Assert
        assertEquals(GameStatus.GAME_OVER, endedState.status)
        assertTrue(endedState.isGameOver)
    }

    @Test
    fun shouldCalculateScoreForClearedLines() {
        // Arrange
        val gameState = GameState().start()
        
        // Act
        val newState = gameState.addScore(1) // 清除1行
        
        // Assert
        assertEquals(100, newState.score) // 1行 = 100分
        assertEquals(1, newState.linesCleared)
    }

    @Test
    fun shouldCalculateScoreForMultipleLines() {
        // Arrange
        val gameState = GameState().start()
        
        // Act
        val newState = gameState.addScore(4) // 清除4行（Tetris）
        
        // Assert
        assertEquals(800, newState.score) // 4行 = 800分
        assertEquals(4, newState.linesCleared)
    }

    @Test
    fun shouldIncreaseLevelBasedOnLinesCleared() {
        // Arrange
        val gameState = GameState().start()
        
        // Act - 清除10行应该升到第2级
        val newState = gameState.addScore(10)
        
        // Assert
        assertEquals(2, newState.level)
        assertEquals(10, newState.linesCleared)
    }
}
