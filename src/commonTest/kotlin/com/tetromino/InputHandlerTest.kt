package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

/**
 * 测试 InputHandler 的基本功能
 * 理由：InputHandler 处理用户输入，需要验证各种操作的正确性
 */
class InputHandlerTest {

    @Test
    fun shouldMoveTetrominoLeft() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        val inputHandler = InputHandler()
        
        // Act
        val result = inputHandler.handleInput(InputAction.MOVE_LEFT, tetromino, board)
        
        // Assert
        assertEquals(Position(4, 10), result.position)
        assertEquals(TetrominoType.T, result.type)
        assertEquals(0, result.rotation)
    }

    @Test
    fun shouldMoveTetrominoRight() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        val inputHandler = InputHandler()
        
        // Act
        val result = inputHandler.handleInput(InputAction.MOVE_RIGHT, tetromino, board)
        
        // Assert
        assertEquals(Position(6, 10), result.position)
        assertEquals(TetrominoType.T, result.type)
        assertEquals(0, result.rotation)
    }

    @Test
    fun shouldMoveTetrominoDown() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        val inputHandler = InputHandler()
        
        // Act
        val result = inputHandler.handleInput(InputAction.SOFT_DROP, tetromino, board)
        
        // Assert
        assertEquals(Position(5, 11), result.position)
        assertEquals(TetrominoType.T, result.type)
        assertEquals(0, result.rotation)
    }

    @Test
    fun shouldRotateTetrominoClockwise() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        val inputHandler = InputHandler()
        
        // Act
        val result = inputHandler.handleInput(InputAction.ROTATE, tetromino, board)
        
        // Assert
        assertEquals(Position(5, 10), result.position)
        assertEquals(TetrominoType.T, result.type)
        assertEquals(1, result.rotation)
    }

    @Test
    fun shouldNotMoveIfCollisionDetected() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(0, 10)) // 已经在左边界
        val inputHandler = InputHandler()
        
        // Act
        val result = inputHandler.handleInput(InputAction.MOVE_LEFT, tetromino, board)
        
        // Assert - 应该保持原位置
        assertEquals(tetromino.position, result.position)
        assertEquals(tetromino.type, result.type)
        assertEquals(tetromino.rotation, result.rotation)
    }

    @Test
    fun shouldNotRotateIfCollisionDetected() {
        // Arrange
        val board = GameBoard(10, 20)
        // 在边界附近放置I型方块，使旋转会导致碰撞
        val tetromino = Tetromino(TetrominoType.I, Position(-1, 10)) // 超出左边界
        val inputHandler = InputHandler()

        // Act
        val result = inputHandler.handleInput(InputAction.ROTATE, tetromino, board)

        // Assert - 应该保持原旋转状态
        assertEquals(tetromino.rotation, result.rotation)
    }

    @Test
    fun shouldHardDropTetromino() {
        // Arrange
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 5))
        val inputHandler = InputHandler()

        // Act
        val result = inputHandler.handleInput(InputAction.HARD_DROP, tetromino, board)

        // Assert - 应该移动到底部
        // T型方块形状：
        //  #
        // ###
        // 所以最底部位置应该是 y=18 (20-2=18，因为方块高度为2)
        assertEquals(Position(5, 18), result.position)
        assertEquals(TetrominoType.T, result.type)
        assertEquals(0, result.rotation)
    }
}
