package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 测试 Renderer 的基本功能
 * 理由：Renderer 负责游戏的视觉呈现，需要验证渲染逻辑的正确性
 */
class RendererTest {

    @Test
    fun shouldCreateRendererWithCorrectDimensions() {
        // Arrange & Act
        val renderer = Renderer(400, 600, 20)
        
        // Assert
        assertEquals(400, renderer.canvasWidth)
        assertEquals(600, renderer.canvasHeight)
        assertEquals(20, renderer.cellSize)
    }

    @Test
    fun shouldCalculateCorrectBoardDimensions() {
        // Arrange
        val renderer = Renderer(400, 600, 20)
        val board = GameBoard(10, 20)
        
        // Act
        val boardWidth = renderer.calculateBoardWidth(board)
        val boardHeight = renderer.calculateBoardHeight(board)
        
        // Assert
        assertEquals(200, boardWidth) // 10 * 20
        assertEquals(400, boardHeight) // 20 * 20
    }

    @Test
    fun shouldCalculateCorrectCellPosition() {
        // Arrange
        val renderer = Renderer(400, 600, 20)
        val position = Position(5, 10)
        
        // Act
        val pixelPosition = renderer.calculatePixelPosition(position)
        
        // Assert
        assertEquals(Position(100, 200), pixelPosition) // 5*20, 10*20
    }

    @Test
    fun shouldCreateRenderData() {
        // Arrange
        val renderer = Renderer(400, 600, 20)
        val board = GameBoard(10, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        val gameState = GameState().start().addScore(2)
        
        // 在游戏板上放置一些方块
        board.setOccupied(Position(0, 19), true)
        board.setOccupied(Position(1, 19), true)
        
        // Act
        val renderData = renderer.createRenderData(board, tetromino, gameState)
        
        // Assert
        assertEquals(gameState.score, renderData.score)
        assertEquals(gameState.level, renderData.level)
        assertEquals(gameState.linesCleared, renderData.linesCleared)
        assertTrue(renderData.boardCells.isNotEmpty())
        assertTrue(renderData.activeTetromino.isNotEmpty())
    }

    @Test
    fun shouldConvertBoardToRenderCells() {
        // Arrange
        val renderer = Renderer(400, 600, 20)
        val board = GameBoard(10, 20)
        
        // 在游戏板上放置一些方块
        board.setOccupied(Position(0, 19), true)
        board.setOccupied(Position(1, 19), true)
        board.setOccupied(Position(5, 10), true)
        
        // Act
        val renderCells = renderer.convertBoardToRenderCells(board)
        
        // Assert
        assertEquals(3, renderCells.size)
        assertTrue(renderCells.any { it.position == Position(0, 19) })
        assertTrue(renderCells.any { it.position == Position(1, 19) })
        assertTrue(renderCells.any { it.position == Position(5, 10) })
    }

    @Test
    fun shouldConvertTetrominoToRenderCells() {
        // Arrange
        val renderer = Renderer(400, 600, 20)
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Act
        val renderCells = renderer.convertTetrominoToRenderCells(tetromino)
        
        // Assert
        assertEquals(4, renderCells.size) // T型方块有4个格子
        assertEquals(TetrominoType.T, renderCells[0].tetrominoType)
        
        // 验证绝对位置
        val absolutePositions = tetromino.getAbsolutePositions()
        renderCells.forEach { cell ->
            assertTrue(absolutePositions.contains(cell.position))
        }
    }
}
