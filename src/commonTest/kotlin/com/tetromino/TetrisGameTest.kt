package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 测试 TetrisGame 的基本功能
 * 理由：TetrisGame 是游戏的主控制器，需要验证游戏逻辑的正确性
 */
class TetrisGameTest {

    @Test
    fun shouldCreateGameWithInitialState() {
        // Arrange & Act
        val game = TetrisGame(10, 20)
        
        // Assert
        assertEquals(GameStatus.NOT_STARTED, game.gameState.status)
        assertEquals(0, game.gameState.score)
        assertEquals(1, game.gameState.level)
        assertNotNull(game.currentTetromino)
    }

    @Test
    fun shouldStartGame() {
        // Arrange
        val game = TetrisGame(10, 20)
        
        // Act
        game.start()
        
        // Assert
        assertEquals(GameStatus.PLAYING, game.gameState.status)
        assertNotNull(game.currentTetromino)
    }

    @Test
    fun shouldPauseAndResumeGame() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        
        // Act - Pause
        game.pause()
        
        // Assert
        assertEquals(GameStatus.PAUSED, game.gameState.status)
        
        // Act - Resume
        game.resume()
        
        // Assert
        assertEquals(GameStatus.PLAYING, game.gameState.status)
    }

    @Test
    fun shouldHandleUserInput() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        val originalPosition = game.currentTetromino?.position
        
        // Act
        game.handleInput(InputAction.MOVE_RIGHT)
        
        // Assert
        val newPosition = game.currentTetromino?.position
        assertEquals(originalPosition?.x?.plus(1), newPosition?.x)
        assertEquals(originalPosition?.y, newPosition?.y)
    }

    @Test
    fun shouldSpawnNewTetrominoWhenCurrentLands() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        val originalTetromino = game.currentTetromino
        
        // Act - 硬下落使方块着陆
        game.handleInput(InputAction.HARD_DROP)
        game.update() // 触发着陆检测
        
        // Assert - 应该生成新方块
        assertNotNull(game.currentTetromino)
        // 新方块应该在顶部
        assertTrue(game.currentTetromino!!.position.y < 5)
    }

    @Test
    fun shouldClearLinesAndUpdateScore() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        
        // 手动填满底行（除了一个位置）
        for (x in 0 until 9) {
            game.board.setOccupied(Position(x, 19), true)
        }
        
        // 放置一个方块来完成这一行
        val tetromino = Tetromino(TetrominoType.O, Position(8, 18))
        game.board.placeTetromino(tetromino)
        
        val originalScore = game.gameState.score
        
        // Act
        game.update() // 触发行清除检测
        
        // Assert
        assertTrue(game.gameState.score > originalScore)
        assertTrue(game.gameState.linesCleared > 0)
    }

    @Test
    fun shouldDetectGameOver() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        
        // 填满游戏板顶部，模拟游戏结束条件
        for (x in 0 until 10) {
            for (y in 0 until 3) {
                game.board.setOccupied(Position(x, y), true)
            }
        }
        
        // Act
        game.update() // 触发游戏结束检测
        
        // Assert
        assertEquals(GameStatus.GAME_OVER, game.gameState.status)
        assertTrue(game.gameState.isGameOver)
    }

    @Test
    fun shouldGenerateRenderData() {
        // Arrange
        val game = TetrisGame(10, 20)
        game.start()
        
        // Act
        val renderData = game.getRenderData()
        
        // Assert
        assertNotNull(renderData)
        assertEquals(game.gameState.score, renderData.score)
        assertEquals(game.gameState.level, renderData.level)
        assertEquals(game.gameState.status, renderData.gameStatus)
        assertTrue(renderData.activeTetromino.isNotEmpty())
    }
}
