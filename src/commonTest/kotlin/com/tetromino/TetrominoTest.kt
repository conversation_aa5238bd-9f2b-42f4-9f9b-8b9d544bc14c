package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 测试 Tetromino 类的基本功能
 * 理由：Tetromino 是游戏中的核心实体，需要验证其位置、旋转等关键业务逻辑
 */
class TetrominoTest {

    @Test
    fun shouldCreateTetrominoWithCorrectProperties() {
        // Arrange & Act
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Assert
        assertEquals(TetrominoType.T, tetromino.type)
        assertEquals(Position(5, 10), tetromino.position)
        assertEquals(0, tetromino.rotation)
    }

    @Test
    fun shouldGetAbsolutePositionsCorrectly() {
        // Arrange
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Act
        val absolutePositions = tetromino.getAbsolutePositions()
        
        // Assert
        assertEquals(4, absolutePositions.size)
        // T 型方块在 (5, 10) 位置的绝对坐标
        assertTrue(absolutePositions.contains(Position(6, 10))) // (1, 0) + (5, 10)
        assertTrue(absolutePositions.contains(Position(5, 11))) // (0, 1) + (5, 10)
        assertTrue(absolutePositions.contains(Position(6, 11))) // (1, 1) + (5, 10)
        assertTrue(absolutePositions.contains(Position(7, 11))) // (2, 1) + (5, 10)
    }

    @Test
    fun shouldMoveCorrectly() {
        // Arrange
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Act
        val movedTetromino = tetromino.move(Position(1, -1))
        
        // Assert
        assertEquals(Position(6, 9), movedTetromino.position)
        assertEquals(TetrominoType.T, movedTetromino.type)
        assertEquals(0, movedTetromino.rotation)
    }

    @Test
    fun shouldRotateClockwise() {
        // Arrange
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Act
        val rotatedTetromino = tetromino.rotateClockwise()
        
        // Assert
        assertEquals(1, rotatedTetromino.rotation)
        assertEquals(Position(5, 10), rotatedTetromino.position)
        assertEquals(TetrominoType.T, rotatedTetromino.type)
    }

    @Test
    fun shouldRotateCounterClockwise() {
        // Arrange
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10))
        
        // Act
        val rotatedTetromino = tetromino.rotateCounterClockwise()
        
        // Assert
        assertEquals(3, rotatedTetromino.rotation) // -1 mod 4 = 3
        assertEquals(Position(5, 10), rotatedTetromino.position)
        assertEquals(TetrominoType.T, rotatedTetromino.type)
    }

    @Test
    fun shouldHandleRotationWrapAround() {
        // Arrange
        val tetromino = Tetromino(TetrominoType.T, Position(5, 10), 3)
        
        // Act
        val rotatedTetromino = tetromino.rotateClockwise()
        
        // Assert
        assertEquals(0, rotatedTetromino.rotation) // 3 + 1 mod 4 = 0
    }
}
