package com.tetromino

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 测试 TetrominoType 枚举的基本功能
 * 理由：TetrominoType 定义了7种标准俄罗斯方块类型，需要验证每种类型的形状定义正确
 */
class TetrominoTypeTest {

    @Test
    fun shouldHaveSevenTetrominoTypes() {
        // Act
        val types = TetrominoType.values()
        
        // Assert
        assertEquals(7, types.size)
        assertTrue(types.contains(TetrominoType.I))
        assertTrue(types.contains(TetrominoType.O))
        assertTrue(types.contains(TetrominoType.T))
        assertTrue(types.contains(TetrominoType.S))
        assertTrue(types.contains(TetrominoType.Z))
        assertTrue(types.contains(TetrominoType.J))
        assertTrue(types.contains(TetrominoType.L))
    }

    @Test
    fun shouldHaveCorrectShapeForIType() {
        // Arrange
        val iType = TetrominoType.I
        
        // Act
        val shape = iType.shape
        
        // Assert
        assertEquals(4, shape.size) // I 型方块有 4 个格子
        assertTrue(shape.contains(Position(0, 1)))
        assertTrue(shape.contains(Position(1, 1)))
        assertTrue(shape.contains(Position(2, 1)))
        assertTrue(shape.contains(Position(3, 1)))
    }

    @Test
    fun shouldHaveCorrectShapeForOType() {
        // Arrange
        val oType = TetrominoType.O
        
        // Act
        val shape = oType.shape
        
        // Assert
        assertEquals(4, shape.size) // O 型方块有 4 个格子
        assertTrue(shape.contains(Position(0, 0)))
        assertTrue(shape.contains(Position(1, 0)))
        assertTrue(shape.contains(Position(0, 1)))
        assertTrue(shape.contains(Position(1, 1)))
    }

    @Test
    fun shouldHaveCorrectShapeForTType() {
        // Arrange
        val tType = TetrominoType.T
        
        // Act
        val shape = tType.shape
        
        // Assert
        assertEquals(4, shape.size) // T 型方块有 4 个格子
        assertTrue(shape.contains(Position(1, 0)))
        assertTrue(shape.contains(Position(0, 1)))
        assertTrue(shape.contains(Position(1, 1)))
        assertTrue(shape.contains(Position(2, 1)))
    }
}
