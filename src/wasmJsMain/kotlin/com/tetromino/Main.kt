package com.tetromino

import kotlinx.browser.document
import kotlinx.browser.window
import org.w3c.dom.HTMLCanvasElement
import org.w3c.dom.CanvasRenderingContext2D
import org.w3c.dom.events.KeyboardEvent

/**
 * WASM 入口点
 */
fun main() {
    println("Tetromino Game Starting...")
    
    val canvas = document.getElementById("gameCanvas") as? HTMLCanvasElement
    if (canvas == null) {
        println("Canvas not found!")
        return
    }
    
    val context = canvas.getContext("2d") as? CanvasRenderingContext2D
    if (context == null) {
        println("2D context not found!")
        return
    }
    
    val game = TetrisGame(10, 20)
    val canvasRenderer = CanvasRenderer(context, 20)
    
    // 开始游戏
    game.start()
    
    // 设置键盘事件监听
    document.addEventListener("keydown", { event ->
        val keyEvent = event as KeyboardEvent
        val action = when (keyEvent.code) {
            "ArrowLeft" -> InputAction.MOVE_LEFT
            "ArrowRight" -> InputAction.MOVE_RIGHT
            "ArrowDown" -> InputAction.SOFT_DROP
            "ArrowUp" -> InputAction.ROTATE
            "Space" -> InputAction.HARD_DROP
            "KeyP" -> InputAction.PAUSE
            else -> null
        }
        
        action?.let { game.handleInput(it) }
        event.preventDefault()
    })
    
    // 游戏循环
    fun gameLoop() {
        // 更新游戏状态
        game.update()
        
        // 渲染游戏
        val renderData = game.getRenderData()
        canvasRenderer.render(renderData)
        
        // 更新 UI
        updateUI(game.gameState)
        
        // 继续循环
        window.requestAnimationFrame { gameLoop() }
    }
    
    // 开始游戏循环
    gameLoop()
}

/**
 * 更新 UI 元素
 */
private fun updateUI(gameState: GameState) {
    document.getElementById("score")?.textContent = gameState.score.toString()
    document.getElementById("lines")?.textContent = gameState.linesCleared.toString()
    document.getElementById("level")?.textContent = gameState.level.toString()
}

/**
 * Canvas 渲染器
 */
class CanvasRenderer(
    private val context: CanvasRenderingContext2D,
    private val cellSize: Int
) {
    
    fun render(renderData: RenderData) {
        // 清空画布
        context.clearRect(0.0, 0.0, 400.0, 600.0)
        
        // 绘制游戏板边框
        context.strokeStyle = "#333"
        context.lineWidth = 2.0
        context.strokeRect(0.0, 0.0, 200.0, 400.0)
        
        // 绘制固定方块
        renderData.boardCells.forEach { cell ->
            drawCell(cell.position, "#666")
        }
        
        // 绘制活动方块
        renderData.activeTetromino.forEach { cell ->
            val color = getTetrominoColor(cell.tetrominoType)
            drawCell(cell.position, color)
        }
        
        // 绘制游戏状态（简化版本以避免 WASM 类型问题）
        if (renderData.gameStatus == GameStatus.GAME_OVER) {
            context.fillStyle = "rgba(0, 0, 0, 0.7)"
            context.fillRect(0.0, 0.0, 200.0, 400.0)
        } else if (renderData.gameStatus == GameStatus.PAUSED) {
            context.fillStyle = "rgba(0, 0, 0, 0.7)"
            context.fillRect(0.0, 0.0, 200.0, 400.0)
        }
    }
    
    private fun drawCell(position: Position, color: String) {
        val x = position.x * cellSize
        val y = position.y * cellSize

        context.fillStyle = color
        context.fillRect(x.toDouble(), y.toDouble(), cellSize.toDouble(), cellSize.toDouble())

        context.strokeStyle = "#000"
        context.lineWidth = 1.0
        context.strokeRect(x.toDouble(), y.toDouble(), cellSize.toDouble(), cellSize.toDouble())
    }
    
    private fun getTetrominoColor(type: TetrominoType?): String {
        return when (type) {
            TetrominoType.I -> "#00f0f0"
            TetrominoType.O -> "#f0f000"
            TetrominoType.T -> "#a000f0"
            TetrominoType.S -> "#00f000"
            TetrominoType.Z -> "#f00000"
            TetrominoType.J -> "#0000f0"
            TetrominoType.L -> "#f0a000"
            null -> "#666"
        }
    }
}
