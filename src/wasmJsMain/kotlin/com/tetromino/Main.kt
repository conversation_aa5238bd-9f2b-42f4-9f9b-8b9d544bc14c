package com.tetromino

/**
 * WASM 入口点 - 简化版本，避免复杂的 DOM 操作
 */
fun main() {
    println("Tetromino Game Starting...")

    // 创建游戏实例
    val game = TetrisGame(10, 20)
    game.start()

    println("Game created and started successfully!")
    println("Current game status: ${game.gameState.status}")
    println("Current tetromino: ${game.currentTetromino?.type}")

    // 演示游戏功能
    demonstrateGameplay(game)
}

/**
 * 演示游戏功能
 */
private fun demonstrateGameplay(game: TetrisGame) {
    println("\n=== 演示游戏功能 ===")

    // 演示移动
    println("1. 测试方块移动:")
    val originalPos = game.currentTetromino?.position
    println("   原始位置: $originalPos")

    game.handleInput(InputAction.MOVE_RIGHT)
    println("   向右移动后: ${game.currentTetromino?.position}")

    game.handleInput(InputAction.MOVE_LEFT)
    println("   向左移动后: ${game.currentTetromino?.position}")

    // 演示旋转
    println("\n2. 测试方块旋转:")
    val originalRotation = game.currentTetromino?.rotation
    println("   原始旋转: $originalRotation")

    game.handleInput(InputAction.ROTATE)
    println("   旋转后: ${game.currentTetromino?.rotation}")

    // 演示硬下落
    println("\n3. 测试硬下落:")
    val beforeDrop = game.currentTetromino?.position
    println("   下落前位置: $beforeDrop")

    game.handleInput(InputAction.HARD_DROP)
    println("   硬下落后位置: ${game.currentTetromino?.position}")

    // 演示游戏更新
    println("\n4. 测试游戏更新:")
    println("   更新前分数: ${game.gameState.score}")
    game.update()
    println("   更新后分数: ${game.gameState.score}")

    // 演示渲染数据
    println("\n5. 测试渲染数据:")
    val renderData = game.getRenderData()
    println("   游戏状态: ${renderData.gameStatus}")
    println("   分数: ${renderData.score}")
    println("   等级: ${renderData.level}")
    println("   已清除行数: ${renderData.linesCleared}")
    println("   活动方块数量: ${renderData.activeTetromino.size}")
    println("   固定方块数量: ${renderData.boardCells.size}")

    println("\n=== 游戏功能演示完成 ===")
}

/**
 * 创建一个简单的游戏渲染器用于演示
 */
class SimpleGameRenderer {

    /**
     * 渲染游戏状态到控制台（用于演示）
     */
    fun renderToConsole(game: TetrisGame) {
        val renderData = game.getRenderData()

        println("\n=== 游戏渲染 ===")
        println("状态: ${renderData.gameStatus}")
        println("分数: ${renderData.score}")
        println("等级: ${renderData.level}")
        println("已清除行数: ${renderData.linesCleared}")

        // 创建一个简单的文本游戏板
        val board = Array(20) { CharArray(10) { '.' } }

        // 绘制固定方块
        renderData.boardCells.forEach { cell ->
            if (cell.position.y in 0 until 20 && cell.position.x in 0 until 10) {
                board[cell.position.y][cell.position.x] = '#'
            }
        }

        // 绘制活动方块
        renderData.activeTetromino.forEach { cell ->
            if (cell.position.y in 0 until 20 && cell.position.x in 0 until 10) {
                val char = when (cell.tetrominoType) {
                    TetrominoType.I -> 'I'
                    TetrominoType.O -> 'O'
                    TetrominoType.T -> 'T'
                    TetrominoType.S -> 'S'
                    TetrominoType.Z -> 'Z'
                    TetrominoType.J -> 'J'
                    TetrominoType.L -> 'L'
                    null -> '*'
                }
                board[cell.position.y][cell.position.x] = char
            }
        }

        // 打印游戏板
        println("游戏板:")
        println("+----------+")
        board.forEach { row ->
            print("|")
            row.forEach { cell -> print(cell) }
            println("|")
        }
        println("+----------+")
    }
}
