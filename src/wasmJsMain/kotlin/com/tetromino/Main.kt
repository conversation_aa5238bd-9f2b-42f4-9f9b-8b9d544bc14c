package com.tetromino

// 全局游戏实例
private var globalGame: TetrisGame? = null
private var gameRenderer: SimpleGameRenderer? = null

/**
 * WASM 入口点
 */
fun main() {
    println("Tetromino Game Starting...")

    // 创建游戏实例
    val result = createGameInternal()
    println("Create game result: $result")

    // 初始渲染
    renderToConsoleInternal()

    // 演示一些游戏操作
    println("\n=== 演示游戏操作 ===")

    // 移动方块
    println("1. 向右移动:")
    handleInputInternal("MOVE_RIGHT")
    renderToConsoleInternal()

    // 旋转方块
    println("2. 旋转方块:")
    handleInputInternal("ROTATE")
    renderToConsoleInternal()

    // 更新游戏状态几次
    println("3. 游戏更新:")
    repeat(3) {
        updateGameInternal()
        println("Update $it completed")
    }
    renderToConsoleInternal()

    println("\n=== WASM 游戏初始化完成 ===")
}

/**
 * 创建新游戏 - 内部函数
 */
fun createGameInternal(): String {
    try {
        globalGame = TetrisGame(10, 20)
        gameRenderer = SimpleGameRenderer()
        globalGame?.start()
        println("Game created in WASM")
        println("Current tetromino: ${globalGame?.currentTetromino?.type}")
        println("Game status: ${globalGame?.gameState?.status}")
        return "Game created successfully"
    } catch (e: Exception) {
        println("Error creating game: ${e.message}")
        return "Error: ${e.message}"
    }
}

/**
 * 处理输入 - 内部函数
 */
fun handleInputInternal(actionStr: String): String {
    val game = globalGame ?: return "No game instance"

    val action = when (actionStr) {
        "MOVE_LEFT" -> InputAction.MOVE_LEFT
        "MOVE_RIGHT" -> InputAction.MOVE_RIGHT
        "SOFT_DROP" -> InputAction.SOFT_DROP
        "HARD_DROP" -> InputAction.HARD_DROP
        "ROTATE" -> InputAction.ROTATE
        "PAUSE" -> InputAction.PAUSE
        else -> return "Invalid action: $actionStr"
    }

    game.handleInput(action)
    return "Action processed: $actionStr"
}

/**
 * 更新游戏状态 - 内部函数
 */
fun updateGameInternal(): String {
    val game = globalGame ?: return "No game instance"
    game.update()
    return "Game updated"
}

/**
 * 获取游戏状态 - 内部函数
 */
fun getGameStateInternal(): String {
    val game = globalGame ?: return "{\"error\": \"No game instance\"}"
    val renderData = game.getRenderData()

    // 简化的 JSON 格式返回
    return buildString {
        append("{")
        append("\"status\": \"${renderData.gameStatus}\",")
        append("\"score\": ${renderData.score},")
        append("\"level\": ${renderData.level},")
        append("\"lines\": ${renderData.linesCleared},")
        append("\"boardCells\": [")
        renderData.boardCells.forEachIndexed { index, cell ->
            if (index > 0) append(",")
            append("{\"x\": ${cell.position.x}, \"y\": ${cell.position.y}}")
        }
        append("],")
        append("\"activeTetromino\": [")
        renderData.activeTetromino.forEachIndexed { index, cell ->
            if (index > 0) append(",")
            append("{\"x\": ${cell.position.x}, \"y\": ${cell.position.y}, \"type\": \"${cell.tetrominoType}\"}")
        }
        append("]")
        append("}")
    }
}

/**
 * 渲染到控制台 - 内部函数
 */
fun renderToConsoleInternal(): String {
    val game = globalGame ?: return "No game instance"
    gameRenderer?.renderToConsole(game)
    return "Rendered to console"
}

/**
 * 演示游戏功能
 */
private fun demonstrateGameplay(game: TetrisGame) {
    println("\n=== 演示游戏功能 ===")

    // 演示移动
    println("1. 测试方块移动:")
    val originalPos = game.currentTetromino?.position
    println("   原始位置: $originalPos")

    game.handleInput(InputAction.MOVE_RIGHT)
    println("   向右移动后: ${game.currentTetromino?.position}")

    game.handleInput(InputAction.MOVE_LEFT)
    println("   向左移动后: ${game.currentTetromino?.position}")

    // 演示旋转
    println("\n2. 测试方块旋转:")
    val originalRotation = game.currentTetromino?.rotation
    println("   原始旋转: $originalRotation")

    game.handleInput(InputAction.ROTATE)
    println("   旋转后: ${game.currentTetromino?.rotation}")

    // 演示硬下落
    println("\n3. 测试硬下落:")
    val beforeDrop = game.currentTetromino?.position
    println("   下落前位置: $beforeDrop")

    game.handleInput(InputAction.HARD_DROP)
    println("   硬下落后位置: ${game.currentTetromino?.position}")

    // 演示游戏更新
    println("\n4. 测试游戏更新:")
    println("   更新前分数: ${game.gameState.score}")
    game.update()
    println("   更新后分数: ${game.gameState.score}")

    // 演示渲染数据
    println("\n5. 测试渲染数据:")
    val renderData = game.getRenderData()
    println("   游戏状态: ${renderData.gameStatus}")
    println("   分数: ${renderData.score}")
    println("   等级: ${renderData.level}")
    println("   已清除行数: ${renderData.linesCleared}")
    println("   活动方块数量: ${renderData.activeTetromino.size}")
    println("   固定方块数量: ${renderData.boardCells.size}")

    println("\n=== 游戏功能演示完成 ===")
}

/**
 * 创建一个简单的游戏渲染器用于演示
 */
class SimpleGameRenderer {

    /**
     * 渲染游戏状态到控制台（用于演示）
     */
    fun renderToConsole(game: TetrisGame) {
        val renderData = game.getRenderData()

        println("\n=== 游戏渲染 ===")
        println("状态: ${renderData.gameStatus}")
        println("分数: ${renderData.score}")
        println("等级: ${renderData.level}")
        println("已清除行数: ${renderData.linesCleared}")

        // 创建一个简单的文本游戏板
        val board = Array(20) { CharArray(10) { '.' } }

        // 绘制固定方块
        renderData.boardCells.forEach { cell ->
            if (cell.position.y in 0 until 20 && cell.position.x in 0 until 10) {
                board[cell.position.y][cell.position.x] = '#'
            }
        }

        // 绘制活动方块
        renderData.activeTetromino.forEach { cell ->
            if (cell.position.y in 0 until 20 && cell.position.x in 0 until 10) {
                val char = when (cell.tetrominoType) {
                    TetrominoType.I -> 'I'
                    TetrominoType.O -> 'O'
                    TetrominoType.T -> 'T'
                    TetrominoType.S -> 'S'
                    TetrominoType.Z -> 'Z'
                    TetrominoType.J -> 'J'
                    TetrominoType.L -> 'L'
                    null -> '*'
                }
                board[cell.position.y][cell.position.x] = char
            }
        }

        // 打印游戏板
        println("游戏板:")
        println("+----------+")
        board.forEach { row ->
            print("|")
            row.forEach { cell -> print(cell) }
            println("|")
        }
        println("+----------+")
    }
}
