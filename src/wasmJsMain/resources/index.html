<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetromino - Kotlin/WASM</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #333;
            background-color: #000;
        }

        .controls {
            margin-top: 20px;
            font-size: 14px;
        }

        .score {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .console-output {
            margin-top: 20px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #444;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Tetromino - Kotlin/WASM</h1>

        <div class="status">
            <div>Status: <span id="status">Loading...</span></div>
            <div>Score: <span id="score">0</span></div>
            <div>Lines: <span id="lines">0</span></div>
            <div>Level: <span id="level">1</span></div>
        </div>

        <canvas id="gameCanvas" width="400" height="600"></canvas>

        <div class="controls">
            <p><strong>Controls:</strong></p>
            <p>← → : Move left/right</p>
            <p>↓ : Soft drop</p>
            <p>↑ : Rotate</p>
            <p>Space : Hard drop</p>
            <p>P : Pause</p>
            <p><button onclick="startDemo()">Start Demo</button></p>
        </div>

        <div class="console-output" id="console">
            Console output will appear here...
        </div>
    </div>

    <script>
        // 重定向 console.log 到页面
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.textContent += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };

        // 游戏实例
        let game = null;
        let renderer = null;

        // 启动演示
        function startDemo() {
            console.log('Starting Tetromino demo...');
            document.getElementById('status').textContent = 'Demo Running';
        }

        // 简单的 Canvas 渲染器
        function createCanvasRenderer() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const cellSize = 20;

            return {
                render: function(renderData) {
                    // 清空画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 绘制游戏板边框
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(0, 0, 200, 400);

                    // 绘制固定方块
                    renderData.boardCells.forEach(cell => {
                        drawCell(ctx, cell.position, '#666', cellSize);
                    });

                    // 绘制活动方块
                    renderData.activeTetromino.forEach(cell => {
                        const color = getTetrominoColor(cell.tetrominoType);
                        drawCell(ctx, cell.position, color, cellSize);
                    });

                    // 更新 UI
                    document.getElementById('score').textContent = renderData.score;
                    document.getElementById('lines').textContent = renderData.linesCleared;
                    document.getElementById('level').textContent = renderData.level;
                    document.getElementById('status').textContent = renderData.gameStatus;
                }
            };
        }

        function drawCell(ctx, position, color, cellSize) {
            const x = position.x * cellSize;
            const y = position.y * cellSize;

            ctx.fillStyle = color;
            ctx.fillRect(x, y, cellSize, cellSize);

            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, cellSize, cellSize);
        }

        function getTetrominoColor(type) {
            switch(type) {
                case 'I': return '#00f0f0';
                case 'O': return '#f0f000';
                case 'T': return '#a000f0';
                case 'S': return '#00f000';
                case 'Z': return '#f00000';
                case 'J': return '#0000f0';
                case 'L': return '#f0a000';
                default: return '#666';
            }
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            if (!game) return;

            let action = null;
            switch(event.code) {
                case 'ArrowLeft': action = 'MOVE_LEFT'; break;
                case 'ArrowRight': action = 'MOVE_RIGHT'; break;
                case 'ArrowDown': action = 'SOFT_DROP'; break;
                case 'ArrowUp': action = 'ROTATE'; break;
                case 'Space': action = 'HARD_DROP'; break;
                case 'KeyP': action = 'PAUSE'; break;
            }

            if (action) {
                // 这里需要调用 WASM 导出的函数
                console.log('Input action:', action);
                event.preventDefault();
            }
        });
    </script>

    <script src="tetromino.js"></script>
</body>
</html>
